import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { WorksheetContainer } from '../../mongodb/schemas/worksheet-container.schema';
import { WorksheetDocumentCacheService } from './worksheet-document-cache.service';
import { WorksheetQuestionMetricsService } from './worksheet-question-metrics.service';
import { RedisService } from '../../redis/redis.service';
import { IExerciseQuestion } from '../../../shared/interfaces/exercise-question.interface';
import { CollectCacheMetrics } from '../interceptors/worksheet-question-metrics.interceptor';

/**
 * Enhanced caching service for worksheet questions with Redis integration,
 * cache warming, and intelligent invalidation strategies
 */
@Injectable()
export class WorksheetQuestionEnhancedCacheService implements OnModuleInit {
  private readonly logger = new Logger(WorksheetQuestionEnhancedCacheService.name);
  
  // Cache configuration
  private readonly CACHE_TTL_SECONDS = 3600; // 1 hour for question data
  private readonly WARM_CACHE_TTL_SECONDS = 7200; // 2 hours for warmed cache
  private readonly POPULAR_THRESHOLD = 10; // Hit count threshold for popular worksheets
  private readonly MAX_CACHE_SIZE = 1000; // Maximum number of cached items
  
  // Cache key prefixes
  private readonly QUESTION_CACHE_PREFIX = 'worksheet_questions:';
  private readonly METADATA_CACHE_PREFIX = 'worksheet_metadata:';
  private readonly PERMISSIONS_CACHE_PREFIX = 'worksheet_permissions:';
  private readonly POPULAR_CACHE_PREFIX = 'popular_worksheets:';

  constructor(
    @InjectModel(WorksheetContainer.name)
    private worksheetContainerModel: Model<WorksheetContainer>,
    private readonly worksheetDocumentCacheService: WorksheetDocumentCacheService,
    private readonly metricsService: WorksheetQuestionMetricsService,
    private readonly redisService: RedisService
  ) {}

  async onModuleInit() {
    // Initialize cache warming on startup
    await this.initializeCacheWarming();
  }

  /**
   * Get worksheet questions from cache with fallback to database
   */
  @CollectCacheMetrics('redis', 'get_questions')
  async getWorksheetQuestions(worksheetId: string, schoolId?: string): Promise<IExerciseQuestion[]> {
    const cacheKey = this.generateQuestionCacheKey(worksheetId);
    const startTime = Date.now();

    try {
      // Try Redis cache first
      const cachedQuestions = await this.redisService.getClient().get(cacheKey);
      if (cachedQuestions) {
        this.logger.debug(`Cache hit for worksheet questions: ${worksheetId}`);
        this.metricsService.recordCacheHit('redis', 'get_questions');
        return JSON.parse(cachedQuestions);
      }

      // Cache miss - get from database
      this.logger.debug(`Cache miss for worksheet questions: ${worksheetId}`);
      this.metricsService.recordCacheMiss('redis', 'get_questions');

      const questionDoc = await this.worksheetContainerModel.findOne({ worksheetId });
      if (!questionDoc) {
        return [];
      }

      const questions = questionDoc.questions || [];
      
      // Cache the result
      await this.cacheWorksheetQuestions(worksheetId, questions, schoolId);
      
      // Update hit count for popularity tracking
      await this.updateHitCount(worksheetId);

      return questions;
    } catch (error) {
      this.logger.error(`Error getting worksheet questions from cache: ${error.message}`);
      // Fallback to direct database query
      const questionDoc = await this.worksheetContainerModel.findOne({ worksheetId });
      return questionDoc?.questions || [];
    }
  }

  /**
   * Cache worksheet questions with TTL
   */
  @CollectCacheMetrics('redis', 'set_questions')
  async cacheWorksheetQuestions(
    worksheetId: string, 
    questions: IExerciseQuestion[], 
    schoolId?: string,
    isWarmCache: boolean = false
  ): Promise<void> {
    const cacheKey = this.generateQuestionCacheKey(worksheetId);
    const ttl = isWarmCache ? this.WARM_CACHE_TTL_SECONDS : this.CACHE_TTL_SECONDS;

    try {
      await this.redisService.getClient().setex(cacheKey, ttl, JSON.stringify(questions));
      
      // Also cache metadata for quick access
      const metadata = {
        worksheetId,
        schoolId,
        questionCount: questions.length,
        lastCached: new Date().toISOString(),
        isWarmCache
      };
      
      const metadataKey = this.generateMetadataCacheKey(worksheetId);
      await this.redisService.getClient().setex(metadataKey, ttl, JSON.stringify(metadata));

      this.logger.debug(`Cached ${questions.length} questions for worksheet ${worksheetId} (TTL: ${ttl}s)`);
    } catch (error) {
      this.logger.error(`Error caching worksheet questions: ${error.message}`);
    }
  }

  /**
   * Get worksheet metadata from cache
   */
  @CollectCacheMetrics('redis', 'get_metadata')
  async getWorksheetMetadata(worksheetId: string): Promise<any> {
    const cacheKey = this.generateMetadataCacheKey(worksheetId);

    try {
      const cachedMetadata = await this.redisService.getClient().get(cacheKey);
      if (cachedMetadata) {
        this.metricsService.recordCacheHit('redis', 'get_metadata');
        return JSON.parse(cachedMetadata);
      }

      this.metricsService.recordCacheMiss('redis', 'get_metadata');
      return null;
    } catch (error) {
      this.logger.error(`Error getting worksheet metadata from cache: ${error.message}`);
      return null;
    }
  }

  /**
   * Cache user permissions for worksheet access
   */
  @CollectCacheMetrics('redis', 'set_permissions')
  async cacheUserPermissions(userId: string, worksheetId: string, permissions: any): Promise<void> {
    const cacheKey = this.generatePermissionsCacheKey(userId, worksheetId);
    const ttl = 1800; // 30 minutes for permissions

    try {
      await this.redisService.getClient().setex(cacheKey, ttl, JSON.stringify(permissions));
      this.logger.debug(`Cached permissions for user ${userId} on worksheet ${worksheetId}`);
    } catch (error) {
      this.logger.error(`Error caching user permissions: ${error.message}`);
    }
  }

  /**
   * Get user permissions from cache
   */
  @CollectCacheMetrics('redis', 'get_permissions')
  async getUserPermissions(userId: string, worksheetId: string): Promise<any> {
    const cacheKey = this.generatePermissionsCacheKey(userId, worksheetId);

    try {
      const cachedPermissions = await this.redisService.getClient().get(cacheKey);
      if (cachedPermissions) {
        this.metricsService.recordCacheHit('redis', 'get_permissions');
        return JSON.parse(cachedPermissions);
      }

      this.metricsService.recordCacheMiss('redis', 'get_permissions');
      return null;
    } catch (error) {
      this.logger.error(`Error getting user permissions from cache: ${error.message}`);
      return null;
    }
  }

  /**
   * Invalidate cache for a specific worksheet
   */
  async invalidateWorksheetCache(worksheetId: string): Promise<void> {
    try {
      const keys = [
        this.generateQuestionCacheKey(worksheetId),
        this.generateMetadataCacheKey(worksheetId)
      ];

      // Also invalidate permission caches for this worksheet
      const permissionPattern = `${this.PERMISSIONS_CACHE_PREFIX}*:${worksheetId}`;
      const permissionKeys = await this.redisService.getClient().keys(permissionPattern);
      keys.push(...permissionKeys);

      // Delete all related cache keys
      if (keys.length > 0) {
        await this.redisService.getClient().del(...keys);
        this.logger.debug(`Invalidated cache for worksheet ${worksheetId} (${keys.length} keys)`);
      }

      // Also invalidate the legacy cache
      await this.worksheetDocumentCacheService.invalidateCache(worksheetId);
    } catch (error) {
      this.logger.error(`Error invalidating worksheet cache: ${error.message}`);
    }
  }

  /**
   * Warm cache for popular worksheets
   */
  async warmPopularWorksheets(): Promise<void> {
    try {
      this.logger.log('Starting cache warming for popular worksheets...');

      // Get popular worksheets based on hit count
      const popularWorksheets = await this.worksheetContainerModel
        .find({ hitCount: { $gte: this.POPULAR_THRESHOLD } })
        .sort({ hitCount: -1 })
        .limit(50)
        .select('worksheetId schoolId questions totalQuestions');

      let warmedCount = 0;
      for (const worksheet of popularWorksheets) {
        try {
          await this.cacheWorksheetQuestions(
            worksheet.worksheetId,
            worksheet.questions || [],
            worksheet.schoolId,
            true // Mark as warm cache
          );
          warmedCount++;
        } catch (error) {
          this.logger.warn(`Failed to warm cache for worksheet ${worksheet.worksheetId}: ${error.message}`);
        }
      }

      // Cache the list of popular worksheets
      const popularList = popularWorksheets.map(w => ({
        worksheetId: w.worksheetId,
        totalQuestions: w.totalQuestions,
        questionCount: w.questions?.length || 0
      }));

      await this.redisService.getClient().setex(
        this.POPULAR_CACHE_PREFIX + 'list',
        3600, // 1 hour
        JSON.stringify(popularList)
      );

      this.logger.log(`Cache warming completed: ${warmedCount}/${popularWorksheets.length} worksheets warmed`);
    } catch (error) {
      this.logger.error(`Error during cache warming: ${error.message}`);
    }
  }

  /**
   * Get cache statistics
   */
  async getCacheStatistics(): Promise<any> {
    try {
      const questionKeys = await this.redisService.getClient().keys(`${this.QUESTION_CACHE_PREFIX}*`);
      const metadataKeys = await this.redisService.getClient().keys(`${this.METADATA_CACHE_PREFIX}*`);
      const permissionKeys = await this.redisService.getClient().keys(`${this.PERMISSIONS_CACHE_PREFIX}*`);

      // Get memory usage info
      const memoryInfo = await this.redisService.getClient().memory('STATS');

      return {
        totalCachedWorksheets: questionKeys.length,
        totalMetadataEntries: metadataKeys.length,
        totalPermissionEntries: permissionKeys.length,
        totalCacheKeys: questionKeys.length + metadataKeys.length + permissionKeys.length,
        memoryUsage: memoryInfo,
        cacheConfiguration: {
          questionTtl: this.CACHE_TTL_SECONDS,
          warmCacheTtl: this.WARM_CACHE_TTL_SECONDS,
          popularThreshold: this.POPULAR_THRESHOLD,
          maxCacheSize: this.MAX_CACHE_SIZE
        }
      };
    } catch (error) {
      this.logger.error(`Error getting cache statistics: ${error.message}`);
      return { error: 'Failed to get cache statistics' };
    }
  }

  /**
   * Initialize cache warming on startup
   */
  private async initializeCacheWarming(): Promise<void> {
    try {
      // Warm cache for popular worksheets on startup
      setTimeout(async () => {
        await this.warmPopularWorksheets();
      }, 5000); // Wait 5 seconds after startup
    } catch (error) {
      this.logger.error(`Error initializing cache warming: ${error.message}`);
    }
  }

  /**
   * Scheduled cache warming (runs every hour)
   */
  @Cron(CronExpression.EVERY_HOUR)
  async scheduledCacheWarming(): Promise<void> {
    await this.warmPopularWorksheets();
  }

  /**
   * Scheduled cache cleanup (runs every 6 hours)
   */
  @Cron('0 */6 * * *')
  async scheduledCacheCleanup(): Promise<void> {
    try {
      this.logger.log('Starting scheduled cache cleanup...');

      // Clean up expired entries from legacy cache
      const expiredCount = await this.worksheetDocumentCacheService.clearExpiredEntries();
      
      // Clean up least recently used items if cache is too large
      await this.cleanupLRUCache();

      this.logger.log(`Cache cleanup completed: ${expiredCount} expired entries removed`);
    } catch (error) {
      this.logger.error(`Error during scheduled cache cleanup: ${error.message}`);
    }
  }

  /**
   * Clean up least recently used cache items
   */
  private async cleanupLRUCache(): Promise<void> {
    try {
      const questionKeys = await this.redisService.getClient().keys(`${this.QUESTION_CACHE_PREFIX}*`);
      
      if (questionKeys.length > this.MAX_CACHE_SIZE) {
        // Get TTL for each key and remove those with shortest TTL
        const keyTtls = await Promise.all(
          questionKeys.map(async (key) => ({
            key,
            ttl: await this.redisService.getClient().ttl(key)
          }))
        );

        // Sort by TTL (ascending) and remove the oldest ones
        keyTtls.sort((a, b) => a.ttl - b.ttl);
        const keysToRemove = keyTtls.slice(0, questionKeys.length - this.MAX_CACHE_SIZE);

        if (keysToRemove.length > 0) {
          await this.redisService.getClient().del(...keysToRemove.map(item => item.key));
          this.logger.debug(`Removed ${keysToRemove.length} LRU cache entries`);
        }
      }
    } catch (error) {
      this.logger.error(`Error during LRU cache cleanup: ${error.message}`);
    }
  }

  /**
   * Update hit count for popularity tracking
   */
  private async updateHitCount(worksheetId: string): Promise<void> {
    try {
      await this.worksheetContainerModel.updateOne(
        { worksheetId },
        {
          $set: { lastModifiedAt: new Date() }
        }
      );
    } catch (error) {
      this.logger.debug(`Error updating hit count: ${error.message}`);
    }
  }

  /**
   * Generate cache key for worksheet questions
   */
  private generateQuestionCacheKey(worksheetId: string): string {
    return `${this.QUESTION_CACHE_PREFIX}${worksheetId}`;
  }

  /**
   * Generate cache key for worksheet metadata
   */
  private generateMetadataCacheKey(worksheetId: string): string {
    return `${this.METADATA_CACHE_PREFIX}${worksheetId}`;
  }

  /**
   * Generate cache key for user permissions
   */
  private generatePermissionsCacheKey(userId: string, worksheetId: string): string {
    return `${this.PERMISSIONS_CACHE_PREFIX}${userId}:${worksheetId}`;
  }
}

import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { RedisService } from '../../redis/redis.service';
import { WorksheetQuestionMetricsService } from './worksheet-question-metrics.service';
import { IExerciseQuestion } from '../../../shared/interfaces/exercise-question.interface';
import { EUserRole } from '../../user/dto/create-user.dto';

/**
 * Advanced Redis-based caching service for worksheet question performance optimization
 * Implements distributed caching, cache warming, and intelligent preloading strategies
 */
@Injectable()
export class WorksheetRedisCacheService implements OnModuleInit {
  private readonly logger = new Logger(WorksheetRedisCacheService.name);

  // Cache configuration
  private readonly CACHE_CONFIG = {
    // TTL configurations (in seconds)
    QUESTION_METADATA_TTL: 1800, // 30 minutes
    USER_PERMISSIONS_TTL: 900, // 15 minutes
    WORKSHEET_SUMMARY_TTL: 3600, // 1 hour
    POPULAR_WORKSHEETS_TTL: 7200, // 2 hours
    SCHOOL_WORKSHEETS_TTL: 1800, // 30 minutes
    USER_RECENT_WORKSHEETS_TTL: 600, // 10 minutes
    
    // Cache size limits
    MAX_CACHED_QUESTIONS_PER_WORKSHEET: 1000,
    MAX_CACHED_WORKSHEETS_PER_SCHOOL: 500,
    MAX_CACHED_USER_PERMISSIONS: 10000,
    
    // Performance thresholds
    POPULAR_WORKSHEET_THRESHOLD: 20,
    PRELOAD_BATCH_SIZE: 50,
    CACHE_WARMING_INTERVAL_HOURS: 2,
  };

  // Cache key prefixes
  private readonly CACHE_KEYS = {
    QUESTION_METADATA: 'wq:metadata:',
    USER_PERMISSIONS: 'wq:perms:',
    WORKSHEET_SUMMARY: 'wq:summary:',
    POPULAR_WORKSHEETS: 'wq:popular:',
    SCHOOL_WORKSHEETS: 'wq:school:',
    USER_RECENT: 'wq:user_recent:',
    COLLABORATION_STATE: 'wq:collab:',
    PERFORMANCE_STATS: 'wq:perf_stats:',
    CACHE_HEALTH: 'wq:health',
  };

  constructor(
    private readonly redisService: RedisService,
    private readonly metricsService: WorksheetQuestionMetricsService,
    private readonly configService: ConfigService
  ) {}

  async onModuleInit() {
    await this.initializeDistributedCache();
  }

  /**
   * Initialize distributed caching with health checks
   */
  private async initializeDistributedCache(): Promise<void> {
    try {
      this.logger.log('Initializing distributed Redis cache for worksheet questions...');

      // Test Redis connectivity
      await this.redisService.getClient().ping();

      // Initialize cache health monitoring
      await this.updateCacheHealth('initialized', new Date());

      // Start cache warming process
      setTimeout(() => this.warmDistributedCache(), 10000); // Wait 10 seconds after startup

      this.logger.log('Distributed Redis cache initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize distributed Redis cache', error);
    }
  }

  /**
   * Cache question metadata for fast access
   */
  async cacheQuestionMetadata(
    worksheetId: string,
    questionId: string,
    metadata: {
      type: string;
      difficulty: string;
      subject: string;
      grade: string;
      estimatedTime?: number;
      tags?: string[];
    }
  ): Promise<void> {
    const cacheKey = `${this.CACHE_KEYS.QUESTION_METADATA}${worksheetId}:${questionId}`;
    
    try {
      const enrichedMetadata = {
        ...metadata,
        worksheetId,
        questionId,
        cachedAt: new Date().toISOString(),
        ttl: this.CACHE_CONFIG.QUESTION_METADATA_TTL
      };

      await this.redisService.getClient().setex(
        cacheKey,
        this.CACHE_CONFIG.QUESTION_METADATA_TTL,
        JSON.stringify(enrichedMetadata)
      );

      this.metricsService.recordCacheHit('redis', 'set_question_metadata');
      this.logger.debug(`Cached metadata for question ${questionId} in worksheet ${worksheetId}`);
    } catch (error) {
      this.logger.error(`Error caching question metadata: ${error.message}`);
      this.metricsService.recordCacheMiss('redis', 'set_question_metadata');
    }
  }

  /**
   * Get question metadata from cache
   */
  async getQuestionMetadata(worksheetId: string, questionId: string): Promise<any> {
    const cacheKey = `${this.CACHE_KEYS.QUESTION_METADATA}${worksheetId}:${questionId}`;
    
    try {
      const cachedData = await this.redisService.getClient().get(cacheKey);
      if (cachedData) {
        this.metricsService.recordCacheHit('redis', 'get_question_metadata');
        return JSON.parse(cachedData);
      }

      this.metricsService.recordCacheMiss('redis', 'get_question_metadata');
      return null;
    } catch (error) {
      this.logger.error(`Error getting question metadata: ${error.message}`);
      return null;
    }
  }

  /**
   * Cache user permissions with role-based TTL
   */
  async cacheUserPermissions(
    userId: string,
    worksheetId: string,
    permissions: {
      canView: boolean;
      canEdit: boolean;
      canDelete: boolean;
      canShare: boolean;
      role: EUserRole;
      schoolId?: string;
    }
  ): Promise<void> {
    const cacheKey = `${this.CACHE_KEYS.USER_PERMISSIONS}${userId}:${worksheetId}`;
    
    try {
      // Adjust TTL based on user role (admins get longer cache)
      const ttl = permissions.role === EUserRole.ADMIN 
        ? this.CACHE_CONFIG.USER_PERMISSIONS_TTL * 2 
        : this.CACHE_CONFIG.USER_PERMISSIONS_TTL;

      const enrichedPermissions = {
        ...permissions,
        userId,
        worksheetId,
        cachedAt: new Date().toISOString(),
        ttl
      };

      await this.redisService.getClient().setex(cacheKey, ttl, JSON.stringify(enrichedPermissions));
      
      this.metricsService.recordCacheHit('redis', 'set_user_permissions');
      this.logger.debug(`Cached permissions for user ${userId} on worksheet ${worksheetId}`);
    } catch (error) {
      this.logger.error(`Error caching user permissions: ${error.message}`);
    }
  }

  /**
   * Get user permissions from cache
   */
  async getUserPermissions(userId: string, worksheetId: string): Promise<any> {
    const cacheKey = `${this.CACHE_KEYS.USER_PERMISSIONS}${userId}:${worksheetId}`;
    
    try {
      const cachedData = await this.redisService.getClient().get(cacheKey);
      if (cachedData) {
        this.metricsService.recordCacheHit('redis', 'get_user_permissions');
        return JSON.parse(cachedData);
      }

      this.metricsService.recordCacheMiss('redis', 'get_user_permissions');
      return null;
    } catch (error) {
      this.logger.error(`Error getting user permissions: ${error.message}`);
      return null;
    }
  }

  /**
   * Cache worksheet summary for quick overview
   */
  async cacheWorksheetSummary(
    worksheetId: string,
    summary: {
      title: string;
      questionCount: number;
      subjects: string[];
      grades: string[];
      difficulty: string;
      estimatedTime: number;
      lastModified: Date;
      schoolId?: string;
      isPublic: boolean;
    }
  ): Promise<void> {
    const cacheKey = `${this.CACHE_KEYS.WORKSHEET_SUMMARY}${worksheetId}`;
    
    try {
      const enrichedSummary = {
        ...summary,
        worksheetId,
        cachedAt: new Date().toISOString(),
        ttl: this.CACHE_CONFIG.WORKSHEET_SUMMARY_TTL
      };

      await this.redisService.getClient().setex(
        cacheKey,
        this.CACHE_CONFIG.WORKSHEET_SUMMARY_TTL,
        JSON.stringify(enrichedSummary)
      );

      this.metricsService.recordCacheHit('redis', 'set_worksheet_summary');
      this.logger.debug(`Cached summary for worksheet ${worksheetId}`);
    } catch (error) {
      this.logger.error(`Error caching worksheet summary: ${error.message}`);
    }
  }

  /**
   * Get worksheet summary from cache
   */
  async getWorksheetSummary(worksheetId: string): Promise<any> {
    const cacheKey = `${this.CACHE_KEYS.WORKSHEET_SUMMARY}${worksheetId}`;
    
    try {
      const cachedData = await this.redisService.getClient().get(cacheKey);
      if (cachedData) {
        this.metricsService.recordCacheHit('redis', 'get_worksheet_summary');
        return JSON.parse(cachedData);
      }

      this.metricsService.recordCacheMiss('redis', 'get_worksheet_summary');
      return null;
    } catch (error) {
      this.logger.error(`Error getting worksheet summary: ${error.message}`);
      return null;
    }
  }

  /**
   * Cache popular worksheets list for a school
   */
  async cachePopularWorksheets(schoolId: string, worksheets: any[]): Promise<void> {
    const cacheKey = `${this.CACHE_KEYS.POPULAR_WORKSHEETS}${schoolId}`;
    
    try {
      const popularData = {
        schoolId,
        worksheets: worksheets.slice(0, 20), // Limit to top 20
        cachedAt: new Date().toISOString(),
        ttl: this.CACHE_CONFIG.POPULAR_WORKSHEETS_TTL
      };

      await this.redisService.getClient().setex(
        cacheKey,
        this.CACHE_CONFIG.POPULAR_WORKSHEETS_TTL,
        JSON.stringify(popularData)
      );

      this.logger.debug(`Cached ${worksheets.length} popular worksheets for school ${schoolId}`);
    } catch (error) {
      this.logger.error(`Error caching popular worksheets: ${error.message}`);
    }
  }

  /**
   * Get popular worksheets from cache
   */
  async getPopularWorksheets(schoolId: string): Promise<any[]> {
    const cacheKey = `${this.CACHE_KEYS.POPULAR_WORKSHEETS}${schoolId}`;
    
    try {
      const cachedData = await this.redisService.getClient().get(cacheKey);
      if (cachedData) {
        const data = JSON.parse(cachedData);
        return data.worksheets || [];
      }

      return [];
    } catch (error) {
      this.logger.error(`Error getting popular worksheets: ${error.message}`);
      return [];
    }
  }

  /**
   * Cache user's recent worksheets
   */
  async cacheUserRecentWorksheets(userId: string, worksheets: any[]): Promise<void> {
    const cacheKey = `${this.CACHE_KEYS.USER_RECENT}${userId}`;
    
    try {
      const recentData = {
        userId,
        worksheets: worksheets.slice(0, 10), // Limit to 10 most recent
        cachedAt: new Date().toISOString(),
        ttl: this.CACHE_CONFIG.USER_RECENT_WORKSHEETS_TTL
      };

      await this.redisService.getClient().setex(
        cacheKey,
        this.CACHE_CONFIG.USER_RECENT_WORKSHEETS_TTL,
        JSON.stringify(recentData)
      );

      this.logger.debug(`Cached ${worksheets.length} recent worksheets for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error caching user recent worksheets: ${error.message}`);
    }
  }

  /**
   * Get user's recent worksheets from cache
   */
  async getUserRecentWorksheets(userId: string): Promise<any[]> {
    const cacheKey = `${this.CACHE_KEYS.USER_RECENT}${userId}`;
    
    try {
      const cachedData = await this.redisService.getClient().get(cacheKey);
      if (cachedData) {
        const data = JSON.parse(cachedData);
        return data.worksheets || [];
      }

      return [];
    } catch (error) {
      this.logger.error(`Error getting user recent worksheets: ${error.message}`);
      return [];
    }
  }

  /**
   * Cache collaboration state for real-time features
   */
  async cacheCollaborationState(
    worksheetId: string,
    state: {
      activeUsers: string[];
      lockedQuestions: { [questionId: string]: string };
      lastActivity: Date;
    }
  ): Promise<void> {
    const cacheKey = `${this.CACHE_KEYS.COLLABORATION_STATE}${worksheetId}`;
    
    try {
      const collabData = {
        ...state,
        worksheetId,
        cachedAt: new Date().toISOString(),
        ttl: 300 // 5 minutes for real-time data
      };

      await this.redisService.getClient().setex(cacheKey, 300, JSON.stringify(collabData));
      this.logger.debug(`Cached collaboration state for worksheet ${worksheetId}`);
    } catch (error) {
      this.logger.error(`Error caching collaboration state: ${error.message}`);
    }
  }

  /**
   * Get collaboration state from cache
   */
  async getCollaborationState(worksheetId: string): Promise<any> {
    const cacheKey = `${this.CACHE_KEYS.COLLABORATION_STATE}${worksheetId}`;
    
    try {
      const cachedData = await this.redisService.getClient().get(cacheKey);
      if (cachedData) {
        return JSON.parse(cachedData);
      }

      return null;
    } catch (error) {
      this.logger.error(`Error getting collaboration state: ${error.message}`);
      return null;
    }
  }

  /**
   * Invalidate all cache entries for a worksheet
   */
  async invalidateWorksheetCache(worksheetId: string): Promise<void> {
    try {
      const patterns = [
        `${this.CACHE_KEYS.QUESTION_METADATA}${worksheetId}:*`,
        `${this.CACHE_KEYS.USER_PERMISSIONS}*:${worksheetId}`,
        `${this.CACHE_KEYS.WORKSHEET_SUMMARY}${worksheetId}`,
        `${this.CACHE_KEYS.COLLABORATION_STATE}${worksheetId}`
      ];

      for (const pattern of patterns) {
        const keys = await this.redisService.getClient().keys(pattern);
        if (keys.length > 0) {
          await this.redisService.getClient().del(...keys);
        }
      }

      this.logger.debug(`Invalidated cache for worksheet ${worksheetId}`);
    } catch (error) {
      this.logger.error(`Error invalidating worksheet cache: ${error.message}`);
    }
  }

  /**
   * Warm distributed cache with popular content
   */
  async warmDistributedCache(): Promise<void> {
    try {
      this.logger.log('Starting distributed cache warming...');

      // This would typically query the database for popular worksheets
      // and preload them into cache. For now, we'll update the health status.
      
      await this.updateCacheHealth('warming_completed', new Date());
      this.logger.log('Distributed cache warming completed');
    } catch (error) {
      this.logger.error('Error during cache warming', error);
    }
  }

  /**
   * Update cache health status
   */
  private async updateCacheHealth(status: string, timestamp: Date): Promise<void> {
    try {
      const healthData = {
        status,
        timestamp: timestamp.toISOString(),
        version: '1.0.0'
      };

      await this.redisService.getClient().setex(
        this.CACHE_KEYS.CACHE_HEALTH,
        3600, // 1 hour
        JSON.stringify(healthData)
      );
    } catch (error) {
      this.logger.error(`Error updating cache health: ${error.message}`);
    }
  }

  /**
   * Get comprehensive cache statistics
   */
  async getCacheStatistics(): Promise<any> {
    try {
      const stats = {
        questionMetadata: await this.getKeyCount(this.CACHE_KEYS.QUESTION_METADATA),
        userPermissions: await this.getKeyCount(this.CACHE_KEYS.USER_PERMISSIONS),
        worksheetSummaries: await this.getKeyCount(this.CACHE_KEYS.WORKSHEET_SUMMARY),
        popularWorksheets: await this.getKeyCount(this.CACHE_KEYS.POPULAR_WORKSHEETS),
        collaborationStates: await this.getKeyCount(this.CACHE_KEYS.COLLABORATION_STATE),
        totalKeys: 0,
        memoryUsage: await this.getMemoryUsage(),
        health: await this.getCacheHealth()
      };

      stats.totalKeys = Object.values(stats).reduce((sum: number, count) => {
        return typeof count === 'number' ? sum + count : sum;
      }, 0);

      return stats;
    } catch (error) {
      this.logger.error(`Error getting cache statistics: ${error.message}`);
      return { error: 'Failed to get cache statistics' };
    }
  }

  /**
   * Scheduled cache maintenance
   */
  @Cron(CronExpression.EVERY_2_HOURS)
  async scheduledCacheMaintenance(): Promise<void> {
    try {
      this.logger.log('Starting scheduled cache maintenance...');

      // Warm cache with popular content
      await this.warmDistributedCache();

      // Update health status
      await this.updateCacheHealth('maintenance_completed', new Date());

      this.logger.log('Scheduled cache maintenance completed');
    } catch (error) {
      this.logger.error('Error during scheduled cache maintenance', error);
    }
  }

  /**
   * Helper method to count keys by pattern
   */
  private async getKeyCount(pattern: string): Promise<number> {
    try {
      const keys = await this.redisService.getClient().keys(`${pattern}*`);
      return keys.length;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Helper method to get memory usage
   */
  private async getMemoryUsage(): Promise<any> {
    try {
      return await this.redisService.getClient().memory('usage');
    } catch (error) {
      return { error: 'Unable to get memory usage' };
    }
  }

  /**
   * Helper method to get cache health
   */
  private async getCacheHealth(): Promise<any> {
    try {
      const healthData = await this.redisService.getClient().get(this.CACHE_KEYS.CACHE_HEALTH);
      return healthData ? JSON.parse(healthData) : { status: 'unknown' };
    } catch (error) {
      return { status: 'error', message: error.message };
    }
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';
import { IExerciseQuestion } from '../../../shared/interfaces/exercise-question.interface';

/**
 * Enum for audit action types
 */
export enum EAuditAction {
  CREATED = 'created',
  UPDATED = 'updated',
  DELETED = 'deleted',
  MOVED = 'moved',
  COPIED = 'copied',
  STATUS_CHANGED = 'status_changed',
  REORDERED = 'reordered',
  LOCKED = 'locked',
  UNLOCKED = 'unlocked',
  BULK_OPERATION = 'bulk_operation',
  IMPORTED = 'imported',
  EXPORTED = 'exported',
}

/**
 * Interface for audit log entry
 */
export interface IAuditLogEntry {
  timestamp: Date;
  userId: string;
  action: EAuditAction;
  changes: Record<string, any>;
  reason?: string;
  metadata?: {
    userAgent?: string;
    ipAddress?: string;
    sessionId?: string;
    worksheetId?: string;
    questionId?: string;
    affectedQuestions?: string[];
  };
}

/**
 * Interface for audit query options
 */
export interface IAuditQueryOptions {
  worksheetId?: string;
  questionId?: string;
  userId?: string;
  action?: EAuditAction | EAuditAction[];
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
  sortBy?: 'timestamp' | 'action' | 'userId';
  sortOrder?: 'asc' | 'desc';
}

/**
 * Service for managing audit logging for worksheet questions
 */
@Injectable()
export class WorksheetQuestionAuditService {
  private readonly logger = new Logger(WorksheetQuestionAuditService.name);

  constructor(
    @InjectModel(WorksheetQuestionDocument.name)
    private worksheetQuestionModel: Model<WorksheetQuestionDocument>,
  ) {}

  /**
   * Log a question creation event
   */
  async logQuestionCreated(
    worksheetId: string,
    questionId: string,
    questionData: Partial<IExerciseQuestion>,
    userId: string,
    metadata?: any
  ): Promise<void> {
    const auditEntry: IAuditLogEntry = {
      timestamp: new Date(),
      userId,
      action: EAuditAction.CREATED,
      changes: {
        questionData,
        worksheetId,
        questionId
      },
      metadata: {
        ...metadata,
        worksheetId,
        questionId
      }
    };

    await this.addAuditEntry(questionId, auditEntry);
    this.logger.log(`Question created: ${questionId} by user ${userId}`);
  }

  /**
   * Log a question update event
   */
  async logQuestionUpdated(
    questionId: string,
    oldData: Partial<IExerciseQuestion>,
    newData: Partial<IExerciseQuestion>,
    userId: string,
    reason?: string,
    metadata?: any
  ): Promise<void> {
    const changes = this.calculateChanges(oldData, newData);
    
    if (Object.keys(changes).length === 0) {
      return; // No actual changes to log
    }

    const auditEntry: IAuditLogEntry = {
      timestamp: new Date(),
      userId,
      action: EAuditAction.UPDATED,
      changes: {
        before: oldData,
        after: newData,
        diff: changes
      },
      reason,
      metadata: {
        ...metadata,
        questionId
      }
    };

    await this.addAuditEntry(questionId, auditEntry);
    this.logger.log(`Question updated: ${questionId} by user ${userId}`);
  }

  /**
   * Log a question deletion event
   */
  async logQuestionDeleted(
    worksheetId: string,
    questionId: string,
    questionData: Partial<IExerciseQuestion>,
    userId: string,
    reason?: string,
    metadata?: any
  ): Promise<void> {
    const auditEntry: IAuditLogEntry = {
      timestamp: new Date(),
      userId,
      action: EAuditAction.DELETED,
      changes: {
        deletedData: questionData,
        worksheetId,
        questionId
      },
      reason,
      metadata: {
        ...metadata,
        worksheetId,
        questionId
      }
    };

    // Since the question is being deleted, we'll log this to a separate audit collection
    // or store it in the worksheet audit log
    this.logger.log(`Question deleted: ${questionId} by user ${userId}`);
  }

  /**
   * Log a question move/reorder event
   */
  async logQuestionMoved(
    questionId: string,
    fromPosition: number,
    toPosition: number,
    fromWorksheetId?: string,
    toWorksheetId?: string,
    userId?: string,
    metadata?: any
  ): Promise<void> {
    const auditEntry: IAuditLogEntry = {
      timestamp: new Date(),
      userId: userId || 'system',
      action: fromWorksheetId !== toWorksheetId ? EAuditAction.MOVED : EAuditAction.REORDERED,
      changes: {
        fromPosition,
        toPosition,
        fromWorksheetId,
        toWorksheetId
      },
      metadata: {
        ...metadata,
        questionId
      }
    };

    await this.addAuditEntry(questionId, auditEntry);
    this.logger.log(`Question ${fromWorksheetId !== toWorksheetId ? 'moved' : 'reordered'}: ${questionId} by user ${userId || 'system'}`);
  }

  /**
   * Log a question status change event
   */
  async logQuestionStatusChanged(
    questionId: string,
    oldStatus: string,
    newStatus: string,
    userId: string,
    reason?: string,
    metadata?: any
  ): Promise<void> {
    const auditEntry: IAuditLogEntry = {
      timestamp: new Date(),
      userId,
      action: EAuditAction.STATUS_CHANGED,
      changes: {
        oldStatus,
        newStatus
      },
      reason,
      metadata: {
        ...metadata,
        questionId
      }
    };

    await this.addAuditEntry(questionId, auditEntry);
    this.logger.log(`Question status changed: ${questionId} from ${oldStatus} to ${newStatus} by user ${userId}`);
  }

  /**
   * Log a bulk operation event
   */
  async logBulkOperation(
    operation: string,
    affectedQuestionIds: string[],
    userId: string,
    operationData?: any,
    reason?: string,
    metadata?: any
  ): Promise<void> {
    const auditEntry: IAuditLogEntry = {
      timestamp: new Date(),
      userId,
      action: EAuditAction.BULK_OPERATION,
      changes: {
        operation,
        operationData,
        affectedCount: affectedQuestionIds.length
      },
      reason,
      metadata: {
        ...metadata,
        affectedQuestions: affectedQuestionIds
      }
    };

    // Log to each affected question
    for (const questionId of affectedQuestionIds) {
      await this.addAuditEntry(questionId, auditEntry);
    }

    this.logger.log(`Bulk operation ${operation}: ${affectedQuestionIds.length} questions by user ${userId}`);
  }

  /**
   * Log a question lock/unlock event
   */
  async logQuestionLockChanged(
    questionId: string,
    isLocked: boolean,
    userId: string,
    lockDuration?: number,
    reason?: string,
    metadata?: any
  ): Promise<void> {
    const auditEntry: IAuditLogEntry = {
      timestamp: new Date(),
      userId,
      action: isLocked ? EAuditAction.LOCKED : EAuditAction.UNLOCKED,
      changes: {
        isLocked,
        lockDuration
      },
      reason,
      metadata: {
        ...metadata,
        questionId
      }
    };

    await this.addAuditEntry(questionId, auditEntry);
    this.logger.log(`Question ${isLocked ? 'locked' : 'unlocked'}: ${questionId} by user ${userId}`);
  }

  /**
   * Get audit history for a specific question
   */
  async getQuestionAuditHistory(
    questionId: string,
    options?: IAuditQueryOptions
  ): Promise<IAuditLogEntry[]> {
    try {
      const question = await this.worksheetQuestionModel.findOne({ questionId });
      
      if (!question || !question.audit?.changeLog) {
        return [];
      }

      let auditLog = question.audit.changeLog;

      // Apply filters
      if (options?.userId) {
        auditLog = auditLog.filter(entry => entry.userId === options.userId);
      }

      if (options?.action) {
        const actions = Array.isArray(options.action) ? options.action : [options.action];
        auditLog = auditLog.filter(entry => actions.includes(entry.action as EAuditAction));
      }

      if (options?.startDate) {
        auditLog = auditLog.filter(entry => entry.timestamp >= options.startDate!);
      }

      if (options?.endDate) {
        auditLog = auditLog.filter(entry => entry.timestamp <= options.endDate!);
      }

      // Apply sorting
      const sortOrder = options?.sortOrder === 'asc' ? 1 : -1;
      auditLog.sort((a, b) => {
        const aValue = a.timestamp;
        const bValue = b.timestamp;
        return (aValue > bValue ? 1 : -1) * sortOrder;
      });

      // Apply pagination
      if (options?.offset || options?.limit) {
        const start = options.offset || 0;
        const end = options.limit ? start + options.limit : undefined;
        auditLog = auditLog.slice(start, end);
      }

      return auditLog as IAuditLogEntry[];
    } catch (error) {
      this.logger.error(`Failed to get audit history for question ${questionId}`, error);
      throw error;
    }
  }

  /**
   * Get audit summary for a worksheet
   */
  async getWorksheetAuditSummary(worksheetId: string): Promise<any> {
    try {
      const questions = await this.worksheetQuestionModel.find({ worksheetId });
      
      const summary = {
        totalQuestions: questions.length,
        totalAuditEntries: 0,
        actionCounts: {} as Record<string, number>,
        userActivity: {} as Record<string, number>,
        recentActivity: [] as IAuditLogEntry[],
        oldestEntry: null as unknown as Date,
        newestEntry: null as unknown as Date
      };

      const allEntries: IAuditLogEntry[] = [];

      for (const question of questions) {
        if (question.audit?.changeLog) {
          allEntries.push(...(question.audit.changeLog as IAuditLogEntry[]));
        }
      }

      summary.totalAuditEntries = allEntries.length;

      // Count actions
      for (const entry of allEntries) {
        summary.actionCounts[entry.action] = (summary.actionCounts[entry.action] || 0) + 1;
        summary.userActivity[entry.userId] = (summary.userActivity[entry.userId] || 0) + 1;
      }

      // Sort by timestamp and get recent activity
      allEntries.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      summary.recentActivity = allEntries.slice(0, 10);

      if (allEntries.length > 0) {
        summary.newestEntry = allEntries[0].timestamp;
        summary.oldestEntry = allEntries[allEntries.length - 1].timestamp;
      }

      return summary;
    } catch (error) {
      this.logger.error(`Failed to get audit summary for worksheet ${worksheetId}`, error);
      throw error;
    }
  }

  /**
   * Add an audit entry to a question's audit log
   */
  private async addAuditEntry(questionId: string, auditEntry: IAuditLogEntry): Promise<void> {
    try {
      await this.worksheetQuestionModel.updateOne(
        { questionId },
        {
          $push: {
            'audit.changeLog': {
              $each: [auditEntry],
              $slice: -100 // Keep only the last 100 entries per question
            }
          },
          $set: {
            'audit.updatedAt': new Date(),
            'audit.updatedBy': auditEntry.userId
          },
          $inc: {
            'audit.version': 1
          }
        }
      );
    } catch (error) {
      this.logger.error(`Failed to add audit entry for question ${questionId}`, error);
      throw error;
    }
  }

  /**
   * Log a question addition event (convenience method)
   */
  async logQuestionAdded(
    worksheetId: string,
    questionId: string,
    user: { sub: string; email: string; role: string; schoolId?: string },
    metadata?: any
  ): Promise<void> {
    const auditEntry: IAuditLogEntry = {
      timestamp: new Date(),
      userId: user.sub,
      action: EAuditAction.CREATED,
      changes: {
        worksheetId,
        questionId,
        userRole: user.role,
        userEmail: user.email
      },
      metadata: {
        ...metadata,
        worksheetId,
        questionId,
        schoolId: user.schoolId
      }
    };

    await this.addAuditEntry(questionId, auditEntry);
    this.logger.log(`Question added to worksheet: ${questionId} by user ${user.sub}`);
  }

  /**
   * Log a question removal event (convenience method)
   */
  async logQuestionRemoved(
    worksheetId: string,
    questionId: string,
    questionData: Partial<IExerciseQuestion>,
    user: { sub: string; email: string; role: string; schoolId?: string },
    reason?: string,
    metadata?: any
  ): Promise<void> {
    const auditEntry: IAuditLogEntry = {
      timestamp: new Date(),
      userId: user.sub,
      action: EAuditAction.DELETED,
      changes: {
        worksheetId,
        questionId,
        deletedQuestionData: questionData,
        userRole: user.role,
        userEmail: user.email
      },
      reason,
      metadata: {
        ...metadata,
        worksheetId,
        questionId,
        schoolId: user.schoolId
      }
    };

    // Log the deletion event
    this.logger.log(`Question removed from worksheet: ${questionId} by user ${user.sub}${reason ? ` (Reason: ${reason})` : ''}`);
  }

  /**
   * Log a question update event (convenience method for partial updates)
   */
  async logQuestionUpdatedInWorksheet(
    worksheetId: string,
    questionId: string,
    user: { sub: string; email: string; role: string; schoolId?: string },
    updateData: any,
    metadata?: any
  ): Promise<void> {
    const auditEntry: IAuditLogEntry = {
      timestamp: new Date(),
      userId: user.sub,
      action: EAuditAction.UPDATED,
      changes: {
        worksheetId,
        questionId,
        updateType: 'partial',
        updatedFields: Object.keys(updateData),
        updateData,
        userRole: user.role,
        userEmail: user.email
      },
      reason: updateData.updateReason,
      metadata: {
        ...metadata,
        worksheetId,
        questionId,
        schoolId: user.schoolId,
        updateType: 'partial'
      }
    };

    await this.addAuditEntry(questionId, auditEntry);
    this.logger.log(`Question updated in worksheet: ${questionId} by user ${user.sub}`);
  }

  /**
   * Log a question replacement event (convenience method for full replacements)
   */
  async logQuestionReplaced(
    worksheetId: string,
    questionId: string,
    user: { sub: string; email: string; role: string; schoolId?: string },
    replacementData: any,
    metadata?: any
  ): Promise<void> {
    const auditEntry: IAuditLogEntry = {
      timestamp: new Date(),
      userId: user.sub,
      action: EAuditAction.UPDATED,
      changes: {
        worksheetId,
        questionId,
        updateType: 'full_replacement',
        replacementData,
        userRole: user.role,
        userEmail: user.email
      },
      reason: replacementData.updateReason,
      metadata: {
        ...metadata,
        worksheetId,
        questionId,
        schoolId: user.schoolId,
        updateType: 'full_replacement'
      }
    };

    await this.addAuditEntry(questionId, auditEntry);
    this.logger.log(`Question replaced in worksheet: ${questionId} by user ${user.sub}`);
  }

  /**
   * Log a bulk question operation event (convenience method)
   */
  async logBulkQuestionOperation(
    worksheetId: string,
    operation: 'bulk_add' | 'bulk_remove' | 'bulk_update',
    user: { sub: string; email: string; role: string; schoolId?: string },
    operationData: {
      successCount: number;
      failureCount: number;
      reason?: string;
    },
    metadata?: any
  ): Promise<void> {
    const auditEntry: IAuditLogEntry = {
      timestamp: new Date(),
      userId: user.sub,
      action: EAuditAction.BULK_OPERATION,
      changes: {
        worksheetId,
        operation,
        successCount: operationData.successCount,
        failureCount: operationData.failureCount,
        userRole: user.role,
        userEmail: user.email
      },
      reason: operationData.reason,
      metadata: {
        ...metadata,
        worksheetId,
        schoolId: user.schoolId,
        operation
      }
    };

    // For bulk operations, we'll log to the worksheet level
    this.logger.log(`Bulk operation ${operation} on worksheet: ${worksheetId} by user ${user.sub} - ${operationData.successCount} successes, ${operationData.failureCount} failures${operationData.reason ? ` (Reason: ${operationData.reason})` : ''}`);
  }

  /**
   * Calculate changes between old and new data
   */
  private calculateChanges(oldData: any, newData: any): Record<string, any> {
    const changes: Record<string, any> = {};

    const allKeys = new Set([...Object.keys(oldData || {}), ...Object.keys(newData || {})]);

    for (const key of allKeys) {
      const oldValue = oldData?.[key];
      const newValue = newData?.[key];

      if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
        changes[key] = {
          from: oldValue,
          to: newValue
        };
      }
    }

    return changes;
  }
}

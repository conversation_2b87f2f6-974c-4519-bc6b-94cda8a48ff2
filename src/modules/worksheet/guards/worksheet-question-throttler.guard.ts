import { Injectable, ExecutionContext } from '@nestjs/common';
import { ThrottlerGuard, ThrottlerException, ThrottlerLimitDetail } from '@nestjs/throttler';
import { EUserRole } from '../../user/dto/create-user.dto';
import { UserContext } from '../../../shared/interfaces/user-context.interface';

/**
 * Role-based throttler guard for worksheet question operations
 * Applies different rate limits based on user roles
 */
@Injectable()
export class WorksheetQuestionThrottlerGuard extends ThrottlerGuard {
  
  /**
   * Role-based rate limits (requests per hour)
   */
  private readonly ROLE_LIMITS = {
    [EUserRole.ADMIN]: 1000,
    [EUserRole.SCHOOL_MANAGER]: 500,
    [EUserRole.TEACHER]: 200,
    [EUserRole.INDEPENDENT_TEACHER]: 100,
  };

  /**
   * Default rate limit for unknown roles
   */
  private readonly DEFAULT_LIMIT = 50;

  /**
   * Time window in seconds (1 hour)
   */
  private readonly TIME_WINDOW = 3600;

  protected async getTracker(req: Record<string, any>): Promise<string> {
    // Use user ID as the tracking key for authenticated requests
    const user: UserContext = req.user;
    if (user?.sub) {
      return `worksheet-question-${user.sub}`;
    }
    
    // Fallback to IP address for unauthenticated requests
    return req.ip || req.connection?.remoteAddress || 'unknown';
  }

  protected async getLimit(context: ExecutionContext): Promise<number> {
    const request = context.switchToHttp().getRequest();
    const user: UserContext = request.user;
    
    if (!user?.role) {
      return this.DEFAULT_LIMIT;
    }

    return this.ROLE_LIMITS[user.role] || this.DEFAULT_LIMIT;
  }

  protected async getTtl(context: ExecutionContext): Promise<number> {
    return this.TIME_WINDOW;
  }

  protected async throwThrottlingException(context: ExecutionContext, throttlerLimitDetail: ThrottlerLimitDetail): Promise<void> {
    const request = context.switchToHttp().getRequest();
    const user: UserContext = request.user;
    const limit = this.ROLE_LIMITS[user?.role] || this.DEFAULT_LIMIT;

    throw new ThrottlerException(
      `Rate limit exceeded. Maximum ${limit} requests per hour allowed for your role.`
    );
  }

  /**
   * Check if the request should be throttled
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user: UserContext = request.user;

    // Skip throttling for system operations or if user is not authenticated
    if (!user) {
      return true;
    }

    // Get the current limit and tracker
    const limit = await this.getLimit(context);
    const tracker = await this.getTracker(request);
    const ttl = await this.getTtl(context);

    // Use the parent class logic for actual throttling
    try {
      return await super.canActivate(context);
    } catch (error) {
      if (error instanceof ThrottlerException) {
        this.throwThrottlingException(context, { limit: 0, ttl: 0, totalHits: 0 });
      }
      throw error;
    }
  }
}

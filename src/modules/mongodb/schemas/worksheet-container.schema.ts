import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { IExerciseQuestion } from '../../../shared/interfaces/exercise-question.interface';

/**
 * Schema for worksheet container that holds multiple questions
 * This is what the bulk processor expects to work with
 */
@Schema({ 
  timestamps: true, 
  collection: 'worksheet_containers',
  optimisticConcurrency: true
})
export class WorksheetContainer extends Document {
  // Worksheet identification
  @Prop({ required: true, unique: true, index: true })
  worksheetId: string;

  // Array of questions
  @Prop({ type: [Object], default: [] })
  questions: IExerciseQuestion[];

  // Question count
  @Prop({ default: 0 })
  totalQuestions: number;

  // Audit fields
  @Prop()
  lastModifiedBy?: string;

  @Prop()
  lastModifiedAt?: Date;

  @Prop()
  createdBy?: string;

  // School association for data isolation
  @Prop({ index: true })
  schoolId?: string;

  // Metadata
  @Prop({ type: Object })
  metadata?: {
    version?: number;
    hasUnsavedChanges?: boolean;
    collaborators?: string[];
    lockStatus?: {
      isLocked: boolean;
      lockedBy?: string;
      lockedAt?: Date;
      lockReason?: string;
    };
  };

  // TTL for cache expiration (optional)
  @Prop({ 
    default: () => {
      // Default TTL: 30 days from now
      const date = new Date();
      date.setDate(date.getDate() + 30);
      return date;
    }
  })
  expiresAt?: Date;
}

export const WorksheetContainerSchema = SchemaFactory.createForClass(WorksheetContainer);

// Add TTL index for automatic expiration
WorksheetContainerSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Add compound indexes for common queries
WorksheetContainerSchema.index({ worksheetId: 1, schoolId: 1 });
WorksheetContainerSchema.index({ schoolId: 1, lastModifiedAt: -1 });
WorksheetContainerSchema.index({ lastModifiedBy: 1, lastModifiedAt: -1 });

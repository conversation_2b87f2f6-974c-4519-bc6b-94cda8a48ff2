import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { EUserRole } from '../dto/create-user.dto'; // Import the EUserRole enum
import BaseEntity from 'src/core/entities/base-entity';
import { Bcrypt } from 'src/core/utils/bcrypt';
import { School } from 'src/modules/school/entities/school.entity';

@Entity('users') // Specify the table name
export class User extends BaseEntity {
  @Column({ length: 100 })
  name: string;

  @Column({ unique: true })
  email: string;

  @Column({ type: 'enum', enum: EUserRole }) // Use enum type for the role
  role: EUserRole;

  @Column()
  password: string;

  @ManyToOne(() => School, { nullable: true })
  @JoinColumn({ name: 'schoolId' })
  school: School;

  @Column({ nullable: true })
  schoolId?: string | null;

  @BeforeInsert()
  async hashPassword() {
    if (this.password) {
      console.log('Hashing password:', this.password); // Debug log
      this.password = await Bcrypt.hash(this.password);
      console.log('Hashed password:', this.password); // Debug log
    }
  }
}
